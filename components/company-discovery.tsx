"use client"

import { Search, Target, MessageSquare, Zap, Building2, Users, CheckCircle, Star } from "lucide-react"

export function CompanyDiscovery() {
  const discoveryFeatures = [
    {
      icon: Search,
      title: "Get discovered by top companies",
      description: "Companies that are looking for talent like you will find you on SkillVerdict easily and contact you right away",
    },
    {
      icon: Target,
      title: "Precision Matching",
      description: "Our AI-powered matching system connects you with candidates whose verified skills align perfectly with your requirements",
    },
  ]


  return (
    <section
      className="bg-gradient-to-b from-slate-50/50 to-white"
      id="company-discovery"
      aria-label="How companies discover and contact verified talent"
    >
      <div className="container-wide">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Building2 className="h-10 w-10 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-heading-2 text-slate-900 mb-4">Get contacted by companies on SkillVerdict</h3>
            <p className="text-body-large text-slate-600 max-w-3xl mx-auto text-pretty">
              Our platform makes it easy for companies to discover and connect with members like you. 
              Simply verify your skills and wait to be contacted by companies who are looking for talent like you.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {discoveryFeatures.map((feature, index) => (
              <div key={index} className="card-interactive p-8 text-left group cursor-pointer">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200">
                  <feature.icon className="h-7 w-7 text-blue-600" aria-hidden="true" />
                </div>
                <h4 className="text-xl font-semibold text-slate-900 mb-3">{feature.title}</h4>
                <p className="text-slate-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 p-8 bg-gradient-to-r from-blue-50 to-emerald-50 rounded-2xl border border-slate-200/50">
          <h3 className="text-heading-2 text-slate-900 mb-4">Ready to be discovered?</h3>
          <p className="text-body-large text-slate-600 mb-6 max-w-2xl mx-auto text-pretty">
            Join thousands of verified professionals who are getting noticed by top companies. 
            Your verified skills are your competitive advantage.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-600">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
              <span>Verified by industry experts</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
              <span>Direct company contact</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
              <span>Possibility to skip technical screenings</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
