"use client"

import { Search, MessageSquare, Zap, Building2, Users, CheckCircle, Star } from "lucide-react"

export function CompanyDiscovery() {
  const discoveryFeatures = [
    {
      icon: Search,
      title: "Companies discover you automatically",
      description: "No job applications needed. Companies find and contact you directly based on your verified skills.",
    },
  ]


  return (
    <section
      className="bg-slate-50"
      id="company-discovery"
      aria-label="How companies discover and contact verified talent"
    >
      <div className="container-wide">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <div className="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-5">
              <Building2 className="h-8 w-8 text-white" aria-hidden="true" />
            </div>
            <h3 className="text-heading-2 text-slate-900 mb-3">Companies come to you</h3>
            <p className="text-body-large text-slate-600 max-w-2xl mx-auto">
             With passive discovery, companies on SkillVerdict discover and contact you directly with opportunities that match your verified skills.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {discoveryFeatures.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-slate-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-5 w-5 text-blue-600" aria-hidden="true" />
                </div>
                <h4 className="text-lg font-semibold text-slate-900 mb-2">{feature.title}</h4>
                <p className="text-slate-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
